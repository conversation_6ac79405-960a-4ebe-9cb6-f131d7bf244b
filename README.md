# TRADOVATE API

# P:
Think hard and create a fully-featured Python SDK for the Tradovate API. Create examples using the MES futures contracts.

Docs:
https://api.tradovate.com/

OpenAPI spec:
openapi.json

# EP:
Create a comprehensive Python SDK for the Tradovate API with the following requirements:

**Core SDK Features:**
- Implement a complete Python client library that covers all Tradovate API endpoints
- Use the provided OpenAPI specification (openapi.json) as the authoritative source for API structure
- Include proper authentication handling (OAuth2/token-based authentication)
- Implement rate limiting and error handling with retry logic
- Support both synchronous and asynchronous operations
- Include comprehensive type hints and docstrings
- Follow Python best practices (PEP 8, proper package structure)

**API Coverage:**
- Market data endpoints (real-time and historical)
- Trading operations (orders, positions, accounts)
- User management and authentication
- WebSocket support for real-time data streams
- Risk management and portfolio operations

**Documentation and Examples:**
- Create detailed documentation with API reference
- Develop practical examples specifically using MES (Micro E-mini S&P 500) futures contracts, including:
  - Connecting to the API and authenticating
  - Retrieving real-time market data for MES contracts
  - Placing, modifying, and canceling orders
  - Managing positions and calculating P&L
  - Implementing basic trading strategies
  - Handling WebSocket data feeds

**Project Structure:**
- Organize as a proper Python package with setup.py/pyproject.toml
- Include unit tests with good coverage
- Add CI/CD configuration
- Create a comprehensive README with installation and usage instructions

**Reference Materials:**
- API Documentation: https://api.tradovate.com/
- OpenAPI Specification: Use the openapi.json file in the workspace
- Focus examples on MES futures contracts (symbol: MES, exchange: CME)

The SDK should be production-ready, well-documented, and easy to use for both beginners and advanced traders.