"""
Data models for the Tradovate SDK.

This package contains all Pydantic models representing API data structures.
"""

from .auth import AccessToken, User, UserSession
from .accounts import Account, CashBalance, MarginSnapshot, TradingPermission
from .contracts import Contract, ContractGroup, ContractMaturity, Product, Exchange
from .orders import Order, OrderStrategy, Fill, ExecutionReport
from .positions import Position, FillPair
from .market_data import Quote, Trade, MarketData, Chart, DepthOfMarket
from .risk import (
    AccountRiskStatus,
    UserAccountPositionLimit,
    UserAccountRiskParameter,
    ContractMargin,
    ProductMargin,
)
from .alerts import Alert, AlertSignal
from .chat import Chat, ChatMessage
from .common import BaseModel, TimestampMixin, IDMixin

__all__ = [
    # Auth models
    "AccessToken",
    "User", 
    "UserSession",
    
    # Account models
    "Account",
    "CashBalance",
    "MarginSnapshot", 
    "TradingPermission",
    
    # Contract models
    "Contract",
    "ContractGroup",
    "ContractMaturity",
    "Product",
    "Exchange",
    
    # Order models
    "Order",
    "OrderStrategy",
    "Fill",
    "ExecutionReport",
    
    # Position models
    "Position",
    "FillPair",
    
    # Market data models
    "Quote",
    "Trade",
    "MarketData",
    "Chart",
    "DepthOfMarket",
    
    # Risk models
    "AccountRiskStatus",
    "UserAccountPositionLimit",
    "UserAccountRiskParameter",
    "ContractMargin",
    "ProductMargin",
    
    # Alert models
    "Alert",
    "AlertSignal",
    
    # Chat models
    "Chat",
    "ChatMessage",
    
    # Base models
    "BaseModel",
    "TimestampMixin",
    "IDMixin",
]
