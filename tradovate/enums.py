"""
Enumerations used throughout the Tradovate SDK.

This module defines all enums that represent various constants and options
used in the Tradovate API.
"""

from enum import Enum, IntEnum
from typing import Union


class Environment(str, Enum):
    """API environment options."""
    DEMO = "demo"
    LIVE = "live"


class OrderAction(str, Enum):
    """Order action types."""
    BUY = "Buy"
    SELL = "Sell"


class OrderType(str, Enum):
    """Order types supported by Tradovate."""
    MARKET = "Market"
    LIMIT = "Limit"
    STOP_MARKET = "StopMarket"
    STOP_LIMIT = "StopLimit"
    TRAILING_STOP = "TrailingStop"
    TRAILING_STOP_LIMIT = "TrailingStopLimit"
    MIT = "MIT"  # Market If Touched
    LIT = "LIT"  # Limit If Touched


class TimeInForce(str, Enum):
    """Time in force options for orders."""
    DAY = "Day"
    GTC = "GTC"  # Good Till Canceled
    GTD = "GTD"  # Good Till Date
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill


class OrderStatus(str, Enum):
    """Order status values."""
    PENDING = "Pending"
    WORKING = "Working"
    FILLED = "Filled"
    CANCELLED = "Cancelled"
    REJECTED = "Rejected"
    EXPIRED = "Expired"
    SUSPENDED = "Suspended"


class PositionSide(str, Enum):
    """Position side indicators."""
    LONG = "Long"
    SHORT = "Short"
    FLAT = "Flat"


class ContractType(str, Enum):
    """Contract types."""
    FUTURE = "Future"
    OPTION = "Option"
    SPREAD = "Spread"
    FOREX = "Forex"
    CRYPTO = "Crypto"


class MarketDataType(str, Enum):
    """Market data types."""
    QUOTE = "Quote"
    TRADE = "Trade"
    DEPTH = "Depth"
    CHART = "Chart"
    DOM = "DOM"  # Depth of Market


class ChartInterval(str, Enum):
    """Chart interval options."""
    TICK = "Tick"
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"


class SubscriptionType(str, Enum):
    """Subscription types for market data."""
    REAL_TIME = "RealTime"
    DELAYED = "Delayed"
    SNAPSHOT = "Snapshot"


class AlertType(str, Enum):
    """Alert types."""
    PRICE = "Price"
    VOLUME = "Volume"
    POSITION = "Position"
    ORDER = "Order"
    ACCOUNT = "Account"
    SYSTEM = "System"


class RiskParameterType(str, Enum):
    """Risk parameter types."""
    POSITION_LIMIT = "PositionLimit"
    ORDER_LIMIT = "OrderLimit"
    LOSS_LIMIT = "LossLimit"
    MARGIN_LIMIT = "MarginLimit"


class AccountType(str, Enum):
    """Account types."""
    DEMO = "Demo"
    LIVE = "Live"
    SIM = "Sim"


class CurrencyCode(str, Enum):
    """Currency codes."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    JPY = "JPY"
    CAD = "CAD"
    AUD = "AUD"
    CHF = "CHF"


class ExchangeCode(str, Enum):
    """Exchange codes."""
    CME = "CME"
    CBOT = "CBOT"
    NYMEX = "NYMEX"
    COMEX = "COMEX"
    ICE = "ICE"
    EUREX = "EUREX"


class ProductCode(str, Enum):
    """Common product codes."""
    ES = "ES"  # E-mini S&P 500
    MES = "MES"  # Micro E-mini S&P 500
    NQ = "NQ"  # E-mini NASDAQ-100
    MNQ = "MNQ"  # Micro E-mini NASDAQ-100
    YM = "YM"  # E-mini Dow Jones
    MYM = "MYM"  # Micro E-mini Dow Jones
    RTY = "RTY"  # E-mini Russell 2000
    M2K = "M2K"  # Micro E-mini Russell 2000
    CL = "CL"  # Crude Oil
    GC = "GC"  # Gold
    SI = "SI"  # Silver
    ZB = "ZB"  # 30-Year Treasury Bond
    ZN = "ZN"  # 10-Year Treasury Note
    ZF = "ZF"  # 5-Year Treasury Note


class WebSocketEventType(str, Enum):
    """WebSocket event types."""
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    ERROR = "error"
    MESSAGE = "message"
    QUOTE = "quote"
    TRADE = "trade"
    DEPTH = "depth"
    ORDER_UPDATE = "order_update"
    POSITION_UPDATE = "position_update"
    ACCOUNT_UPDATE = "account_update"


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# Type aliases for convenience
OrderActionType = Union[OrderAction, str]
OrderTypeType = Union[OrderType, str]
TimeInForceType = Union[TimeInForce, str]
EnvironmentType = Union[Environment, str]
