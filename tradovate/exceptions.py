"""
Exception classes for the Tradovate SDK.

This module defines all custom exceptions used throughout the SDK to provide
clear error handling and debugging information.
"""

from typing import Optional, Dict, Any


class TradovateError(Exception):
    """Base exception class for all Tradovate SDK errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class AuthenticationError(TradovateError):
    """Raised when authentication fails or tokens are invalid."""
    pass


class AuthorizationError(TradovateError):
    """Raised when the user lacks permission for a requested operation."""
    pass


class RateLimitError(TradovateError):
    """Raised when API rate limits are exceeded."""
    
    def __init__(
        self,
        message: str,
        retry_after: Optional[int] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.retry_after = retry_after


class APIError(TradovateError):
    """Raised when the API returns an error response."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.status_code = status_code
        self.response_data = response_data or {}


class ValidationError(TradovateError):
    """Raised when input validation fails."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.field = field
        self.value = value


class NetworkError(TradovateError):
    """Raised when network-related errors occur."""
    
    def __init__(
        self,
        message: str,
        original_error: Optional[Exception] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.original_error = original_error


class TimeoutError(NetworkError):
    """Raised when requests timeout."""
    pass


class ConnectionError(NetworkError):
    """Raised when connection to the API fails."""
    pass


class WebSocketError(TradovateError):
    """Raised when WebSocket operations fail."""
    pass


class ConfigurationError(TradovateError):
    """Raised when SDK configuration is invalid."""
    pass


class DataError(TradovateError):
    """Raised when data parsing or processing fails."""
    pass


class OrderError(TradovateError):
    """Raised when order operations fail."""
    
    def __init__(
        self,
        message: str,
        order_id: Optional[str] = None,
        order_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.order_id = order_id
        self.order_data = order_data or {}


class PositionError(TradovateError):
    """Raised when position operations fail."""
    
    def __init__(
        self,
        message: str,
        position_id: Optional[str] = None,
        position_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.position_id = position_id
        self.position_data = position_data or {}


class AccountError(TradovateError):
    """Raised when account operations fail."""
    
    def __init__(
        self,
        message: str,
        account_id: Optional[str] = None,
        account_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, error_code, details)
        self.account_id = account_id
        self.account_data = account_data or {}


class MarketDataError(TradovateError):
    """Raised when market data operations fail."""
    pass


class RiskManagementError(TradovateError):
    """Raised when risk management operations fail."""
    pass
