"""
Main client classes for the Tradovate SDK.

This module provides the primary interface for interacting with the Tradovate API,
including both synchronous and asynchronous clients.
"""

import asyncio
from typing import Optional, Dict, Any, Union
import httpx
from datetime import datetime, timedelta

from .enums import Environment
from .exceptions import (
    TradovateError,
    AuthenticationError,
    ConfigurationError,
    NetworkError,
)
from .utils import Config, get_logger, RateLimiter
from .api import (
    AuthAPI,
    AccountsAPI,
    ContractsAPI,
    OrdersAPI,
    PositionsAPI,
    MarketDataAPI,
    RiskAPI,
    UsersAPI,
    AlertsAPI,
    ChatAPI,
)
from .websocket import WebSocketClient


class BaseClient:
    """Base client class with common functionality."""
    
    def __init__(
        self,
        api_key: str,
        api_secret: str,
        environment: Union[Environment, str] = Environment.DEMO,
        app_id: str = "TradovateSDK",
        app_version: str = "1.0.0",
        timeout: float = 30.0,
        rate_limit: Optional[int] = None,
        **kwargs
    ) -> None:
        """
        Initialize the base client.
        
        Args:
            api_key: Your Tradovate API key
            api_secret: Your Tradovate API secret
            environment: API environment ("demo" or "live")
            app_id: Application identifier
            app_version: Application version
            timeout: Request timeout in seconds
            rate_limit: Maximum requests per second (None for no limit)
            **kwargs: Additional configuration options
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.environment = Environment(environment)
        self.app_id = app_id
        self.app_version = app_version
        self.timeout = timeout
        
        # Set base URL based on environment
        if self.environment == Environment.DEMO:
            self.base_url = "https://demo.tradovateapi.com/v1"
        else:
            self.base_url = "https://live.tradovateapi.com/v1"
        
        # Initialize rate limiter if specified
        self.rate_limiter = RateLimiter(rate_limit) if rate_limit else None
        
        # Authentication state
        self.access_token: Optional[str] = None
        self.md_access_token: Optional[str] = None
        self.user_id: Optional[int] = None
        self.token_expires_at: Optional[datetime] = None
        
        # Logger
        self.logger = get_logger(__name__)
        
        # Configuration
        self.config = Config(**kwargs)
        
        self.logger.info(f"Initialized Tradovate client for {environment} environment")
    
    @property
    def is_authenticated(self) -> bool:
        """Check if the client is currently authenticated."""
        return (
            self.access_token is not None and
            self.token_expires_at is not None and
            datetime.utcnow() < self.token_expires_at
        )
    
    def _get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """Get HTTP headers for API requests."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"{self.app_id}/{self.app_version}",
        }
        
        if include_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    def _validate_config(self) -> None:
        """Validate client configuration."""
        if not self.api_key:
            raise ConfigurationError("API key is required")
        if not self.api_secret:
            raise ConfigurationError("API secret is required")


class TradovateClient(BaseClient):
    """
    Synchronous Tradovate API client.
    
    This client provides a synchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        
        # Initialize HTTP client
        self.http_client = httpx.Client(
            timeout=self.timeout,
            headers=self._get_headers(include_auth=False)
        )
        
        # Initialize API modules
        self.auth = AuthAPI(self)
        self.accounts = AccountsAPI(self)
        self.contracts = ContractsAPI(self)
        self.orders = OrdersAPI(self)
        self.positions = PositionsAPI(self)
        self.market_data = MarketDataAPI(self)
        self.risk = RiskAPI(self)
        self.users = UsersAPI(self)
        self.alerts = AlertsAPI(self)
        self.chat = ChatAPI(self)
        
        # WebSocket client (initialized when needed)
        self._websocket_client: Optional[WebSocketClient] = None
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        return self.auth.authenticate(username, password)
    
    def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.
        
        Returns:
            Token renewal response data
            
        Raises:
            AuthenticationError: If token renewal fails
        """
        return self.auth.renew_token()
    
    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            self.http_client.close()
        if self._websocket_client:
            self._websocket_client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class TradovateAsyncClient(BaseClient):
    """
    Asynchronous Tradovate API client.
    
    This client provides an asynchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        
        # HTTP client will be initialized in async context
        self.http_client: Optional[httpx.AsyncClient] = None
        
        # Initialize API modules
        self.auth = AuthAPI(self)
        self.accounts = AccountsAPI(self)
        self.contracts = ContractsAPI(self)
        self.orders = OrdersAPI(self)
        self.positions = PositionsAPI(self)
        self.market_data = MarketDataAPI(self)
        self.risk = RiskAPI(self)
        self.users = UsersAPI(self)
        self.alerts = AlertsAPI(self)
        self.chat = ChatAPI(self)
        
        # WebSocket client (initialized when needed)
        self._websocket_client: Optional[WebSocketClient] = None
    
    async def _ensure_http_client(self) -> None:
        """Ensure HTTP client is initialized."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(
                timeout=self.timeout,
                headers=self._get_headers(include_auth=False)
            )
    
    async def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        await self._ensure_http_client()
        return await self.auth.authenticate(username, password)
    
    async def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.
        
        Returns:
            Token renewal response data
            
        Raises:
            AuthenticationError: If token renewal fails
        """
        await self._ensure_http_client()
        return await self.auth.renew_token()
    
    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    async def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            await self.http_client.aclose()
        if self._websocket_client:
            await self._websocket_client.close()
    
    async def __aenter__(self):
        await self._ensure_http_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
